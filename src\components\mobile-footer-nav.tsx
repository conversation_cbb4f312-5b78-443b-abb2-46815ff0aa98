"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  Home,
  Package,
  Wrench,
  ArrowLeftRight,
  MoreHorizontal,
  HardHat,
  BarChart3,
  QrCode,
  Users,
} from "lucide-react"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { useAuth } from "@/contexts/auth-context"
import { cn } from "@/lib/utils"

export function MobileFooterNav() {
  const pathname = usePathname()
  const { user } = useAuth()

  const mainNavItems = [
    { href: "/dashboard", icon: Home, label: "Tổng quan" },
    { href: "/equipment", icon: Package, label: "Thiết bị" },
    { href: "/repair-requests", icon: Wrench, label: "S<PERSON>a chữa" },
    { href: "/transfers", icon: ArrowLeftRight, label: "<PERSON><PERSON> chuyển" },
  ]

  const moreNavItems = React.useMemo(() => {
    const baseItems = [
      { href: "/maintenance", icon: HardHat, label: "Bảo trì" },
      { href: "/reports", icon: BarChart3, label: "Báo cáo" },
      { href: "/qr-scanner", icon: QrCode, label: "Quét QR" },
    ]

    if (user?.role === 'admin') {
      baseItems.push({ href: "/users", icon: Users, label: "Người dùng" })
    }

    return baseItems
  }, [user?.role])

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 border-t bg-background/95 backdrop-blur-sm md:hidden">
      <nav className="grid h-16 grid-cols-5 items-center">
        {mainNavItems.map(({ href, icon: Icon, label }) => (
          <Link
            key={label}
            href={href}
            className={cn(
              "flex flex-col items-center justify-center gap-1 text-xs font-medium transition-colors",
              pathname === href ? "text-primary" : "text-muted-foreground hover:text-primary"
            )}
          >
            <Icon className="h-5 w-5" />
            <span>{label}</span>
          </Link>
        ))}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="flex flex-col items-center justify-center gap-1 text-xs font-medium text-muted-foreground h-full">
              <MoreHorizontal className="h-5 w-5" />
              <span>Thêm</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="mb-2">
            {moreNavItems.map(({ href, icon: Icon, label }) => (
              <DropdownMenuItem key={label} asChild>
                <Link href={href} className="flex items-center gap-2">
                  <Icon className="h-4 w-4" />
                  {label}
                </Link>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </nav>
    </div>
  )
}
